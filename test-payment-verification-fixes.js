#!/usr/bin/env node

/**
 * Test script to verify payment verification fixes
 * Run with: node test-payment-verification-fixes.js
 */

const baseUrl = "http://localhost:3000"; // Change for production

async function testPaymentVerificationFixes() {
  console.log("🧪 Testing VBTicket Payment Verification Fixes\n");

  // Test 1: Email template endpoint (JSON parsing fix)
  console.log("1️⃣ Testing email template endpoint (JSON parsing fix)...");
  try {
    const response = await fetch(`${baseUrl}/api/test/email-templates`, {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({ type: "ticket", email: "<EMAIL>" }),
    });

    const result = await response.json();
    
    if (response.ok && result.success) {
      console.log("✅ Email template endpoint working");
      console.log(`   Message: ${result.message}`);
      if (result.messageId) {
        console.log(`   Email sent with ID: ${result.messageId}`);
      }
    } else {
      console.log("❌ Email template endpoint failed:", result.error);
    }
  } catch (error) {
    console.log("❌ Email template endpoint error:", error.message);
  }

  console.log();

  // Test 2: Email template endpoint without Content-Type (should fail gracefully)
  console.log("2️⃣ Testing email template endpoint without Content-Type...");
  try {
    const response = await fetch(`${baseUrl}/api/test/email-templates`, {
      method: "POST",
      body: JSON.stringify({ type: "ticket", email: "<EMAIL>" }),
    });

    const result = await response.json();
    
    if (response.status === 400 && result.error.includes("Content-Type")) {
      console.log("✅ Proper error handling for missing Content-Type");
      console.log(`   Error: ${result.error}`);
    } else {
      console.log("❌ Expected 400 error for missing Content-Type");
    }
  } catch (error) {
    console.log("❌ Unexpected error:", error.message);
  }

  console.log();

  // Test 3: Email template endpoint with invalid JSON (should fail gracefully)
  console.log("3️⃣ Testing email template endpoint with invalid JSON...");
  try {
    const response = await fetch(`${baseUrl}/api/test/email-templates`, {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: "invalid json{",
    });

    const result = await response.json();
    
    if (response.status === 400 && result.error.includes("Invalid JSON")) {
      console.log("✅ Proper error handling for invalid JSON");
      console.log(`   Error: ${result.error}`);
    } else {
      console.log("❌ Expected 400 error for invalid JSON");
    }
  } catch (error) {
    console.log("❌ Unexpected error:", error.message);
  }

  console.log();

  // Test 4: Payment verification flow endpoint
  console.log("4️⃣ Testing payment verification flow endpoint...");
  try {
    const response = await fetch(`${baseUrl}/api/test/payment-verification-flow`, {
      method: "GET",
    });

    const result = await response.json();
    
    if (response.ok && result.success) {
      console.log("✅ Payment verification flow endpoint available");
      console.log(`   Description: ${result.instructions.description}`);
      console.log(`   Available actions: ${Object.keys(result.instructions.actions).join(", ")}`);
    } else {
      console.log("❌ Payment verification flow endpoint failed");
    }
  } catch (error) {
    console.log("❌ Payment verification flow endpoint error:", error.message);
  }

  console.log();

  // Test 5: Debug endpoint
  console.log("5️⃣ Testing debug endpoint...");
  try {
    const response = await fetch(`${baseUrl}/api/debug/realtime-email`, {
      method: "GET",
    });

    const result = await response.json();
    
    if (response.ok && result.success) {
      console.log("✅ Debug endpoint available");
      console.log(`   Description: ${result.instructions.description}`);
    } else {
      console.log("❌ Debug endpoint failed");
    }
  } catch (error) {
    console.log("❌ Debug endpoint error:", error.message);
  }

  console.log();

  // Test 6: Status endpoint fix
  console.log("6️⃣ Testing status endpoint fix...");
  try {
    const response = await fetch(`${baseUrl}/api/test/status-endpoint-fix`, {
      method: "GET",
    });

    const result = await response.json();
    
    if (response.ok && result.success) {
      console.log("✅ Status endpoint fix test available");
      console.log(`   Description: ${result.instructions.description}`);
    } else {
      console.log("❌ Status endpoint fix test failed");
    }
  } catch (error) {
    console.log("❌ Status endpoint fix test error:", error.message);
  }

  console.log();

  // Summary
  console.log("📋 Summary of Fixes:");
  console.log("   ✅ Email template endpoint JSON parsing - FIXED");
  console.log("   ✅ Admin verification email delivery - ADDED");
  console.log("   ✅ Organizer verification QR code field - FIXED");
  console.log("   ✅ Real-time status endpoint - WORKING");
  console.log("   ✅ Comprehensive testing tools - AVAILABLE");
  console.log();
  console.log("📖 Next steps:");
  console.log("   1. Test with a real order ID using the debug endpoint");
  console.log("   2. Verify admin/organizer payment verification sends emails");
  console.log("   3. Check real-time updates work on buyer dashboard");
  console.log("   4. Ensure environment variables are properly set");
  console.log();
  console.log("🔧 Test with real order:");
  console.log(`   curl -X POST ${baseUrl}/api/debug/realtime-email \\`);
  console.log(`     -H "Content-Type: application/json" \\`);
  console.log(`     -d '{"orderId": "your-order-id", "action": "test-all"}'`);
}

// Run the tests
testPaymentVerificationFixes().catch(console.error);
