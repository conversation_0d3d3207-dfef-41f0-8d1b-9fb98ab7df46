{"name": "vbticket", "version": "0.1.0", "private": true, "type": "module", "scripts": {"build": "npm run prisma:generate && next build", "build:vercel": "SKIP_ENV_VALIDATION=true prisma generate && SKIP_ENV_VALIDATION=true next build", "build:windows": "npm run prisma:clean && npm run prisma:generate && next build", "check": "next lint && tsc --noEmit", "db:generate": "prisma migrate dev", "db:migrate": "prisma migrate deploy", "db:push": "prisma db push", "db:studio": "prisma studio", "dev": "next dev --turbo", "format:check": "prettier --check \"**/*.{ts,tsx,js,jsx,mdx}\" --cache", "format:write": "prettier --write \"**/*.{ts,tsx,js,jsx,mdx}\" --cache", "postinstall": "npm run prisma:generate", "lint": "next lint", "lint:fix": "next lint --fix", "preview": "next build && next start", "prisma:clean": "rimraf node_modules/.prisma && rimraf prisma/generated", "prisma:generate": "prisma generate", "start": "next start", "typecheck": "tsc --noEmit"}, "dependencies": {"@auth/prisma-adapter": "^2.7.2", "@dnd-kit/modifiers": "^9.0.0", "@dnd-kit/sortable": "^10.0.0", "@hookform/resolvers": "^5.0.1", "@prisma/client": "^6.7.0", "@radix-ui/react-accordion": "^1.2.10", "@radix-ui/react-alert-dialog": "^1.1.13", "@radix-ui/react-avatar": "^1.1.9", "@radix-ui/react-checkbox": "^1.3.1", "@radix-ui/react-dialog": "^1.1.13", "@radix-ui/react-dropdown-menu": "^2.1.14", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.6", "@radix-ui/react-navigation-menu": "^1.2.12", "@radix-ui/react-popover": "^1.1.13", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-select": "^2.2.4", "@radix-ui/react-separator": "^1.1.6", "@radix-ui/react-slot": "^1.2.2", "@radix-ui/react-tabs": "^1.1.11", "@radix-ui/react-toast": "^1.2.13", "@radix-ui/react-toggle": "^1.1.8", "@radix-ui/react-toggle-group": "^1.1.9", "@radix-ui/react-tooltip": "^1.2.6", "@t3-oss/env-nextjs": "^0.12.0", "@tanstack/react-query": "^5.28.0", "@tanstack/react-query-devtools": "^5.28.0", "@tanstack/react-table": "^8.21.3", "@zxing/browser": "^0.1.5", "@zxing/library": "^0.21.3", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "cloudinary": "^2.6.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "h3": "^1.15.3", "jspdf": "^2.5.2", "lucide-react": "^0.508.0", "next": "^15.2.3", "next-auth": "^4.24.7", "next-cloudinary": "^6.16.0", "next-themes": "^0.4.6", "qrcode": "^1.5.4", "react": "^19.0.0", "react-day-picker": "^9.7.0", "react-dom": "^19.1.0", "react-hook-form": "^7.56.3", "recharts": "^2.15.3", "resend": "^4.5.2", "sonner": "^2.0.3", "swr": "^2.3.3", "tailwind-merge": "^3.2.0", "tailwindcss-animate": "^1.0.7", "xendit-node": "^6.4.0", "zod": "^3.24.4"}, "devDependencies": {"@eslint/eslintrc": "^3.3.1", "@tailwindcss/postcss": "^4.0.15", "@types/node": "^20.14.10", "@types/qrcode": "^1.5.5", "@types/react": "^19.0.0", "@types/react-dom": "^19.0.0", "@types/uuid": "^10.0.0", "eslint": "^9.23.0", "eslint-config-next": "^15.2.3", "postcss": "^8.5.3", "prettier": "^3.5.3", "prettier-plugin-tailwindcss": "^0.6.11", "prisma": "^6.5.0", "rimraf": "^6.0.1", "tailwindcss": "^4.0.15", "tsx": "^4.19.4", "tw-animate-css": "^1.2.9", "typescript": "^5.8.2", "typescript-eslint": "^8.27.0"}, "ct3aMetadata": {"initVersion": "7.39.3"}, "packageManager": "npm@10.9.2"}