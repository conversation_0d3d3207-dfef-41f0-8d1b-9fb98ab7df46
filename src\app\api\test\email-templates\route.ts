import { NextRequest, NextResponse } from "next/server";
import { emailService } from "~/lib/email-service";

/**
 * POST /api/test/email-templates
 * Test email templates by sending sample emails
 */
export async function POST(request: NextRequest) {
  try {
    // Check if request has content
    const contentType = request.headers.get("content-type");
    if (!contentType || !contentType.includes("application/json")) {
      return NextResponse.json(
        {
          success: false,
          error: "Content-Type must be application/json",
          example: {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify({ type: "ticket", email: "<EMAIL>" })
          }
        },
        { status: 400 }
      );
    }

    let body;
    try {
      body = await request.json();
    } catch (jsonError) {
      return NextResponse.json(
        {
          success: false,
          error: "Invalid JSON in request body",
          details: jsonError instanceof Error ? jsonError.message : "JSON parsing failed",
          example: {
            type: "ticket",
            email: "<EMAIL>"
          }
        },
        { status: 400 }
      );
    }

    const { type, email } = body;

    if (!email) {
      return NextResponse.json(
        { success: false, error: "Email address is required" },
        { status: 400 }
      );
    }

    let result;

    switch (type) {
      case 'verification':
        result = await emailService.sendAccountVerification({
          to: email,
          userName: "John Doe",
          verificationUrl: "https://vbticket.com/verify?token=sample-token-123"
        });
        break;

      case 'ticket':
        result = await emailService.sendTicketDelivery({
          to: email,
          customerName: "John Doe",
          event: {
            title: "Tech Conference 2024 - Sample Event",
            date: "Sabtu, 15 Juni 2025",
            time: "09:00 - 17:00 WIB",
            location: "Jakarta Convention Center",
            address: "Jl. Gatot Subroto, Jakarta Pusat, DKI Jakarta 10270",
            image: "https://placehold.co/600x300/3b82f6/ffffff?text=Tech+Conference+2024"
          },
          order: {
            invoiceNumber: "INV-**********-123",
            totalAmount: 500000,
            paymentDate: "10 Juni 2025, 14:30 WIB"
          },
          tickets: [
            {
              id: "ticket-1",
              ticketNumber: "TC2024-001-123456",
              ticketType: "Early Bird",
              holderName: "John Doe",
              qrCode: "https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=TC2024-001-123456"
            },
            {
              id: "ticket-2",
              ticketNumber: "TC2024-001-123457",
              ticketType: "Early Bird",
              holderName: "Jane Smith",
              qrCode: "https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=TC2024-001-123457"
            }
          ]
        });
        break;

      default:
        return NextResponse.json(
          { success: false, error: "Invalid email type. Use 'verification' or 'ticket'" },
          { status: 400 }
        );
    }

    if (result.success) {
      return NextResponse.json({
        success: true,
        message: `${type} email sent successfully`,
        messageId: result.messageId
      });
    } else {
      return NextResponse.json(
        { success: false, error: result.error },
        { status: 500 }
      );
    }

  } catch (error: any) {
    console.error("Error testing email template:", error);
    return NextResponse.json(
      { success: false, error: error.message || "Failed to send test email" },
      { status: 500 }
    );
  }
}

/**
 * GET /api/test/email-templates
 * Get information about available email templates
 */
export async function GET() {
  return NextResponse.json({
    success: true,
    data: {
      templates: [
        {
          type: "verification",
          name: "Account Verification",
          description: "Email sent to users to verify their email address",
          testEndpoint: "POST /api/test/email-templates",
          payload: {
            type: "verification",
            email: "<EMAIL>"
          }
        },
        {
          type: "ticket",
          name: "Ticket Delivery",
          description: "Email sent to customers with their purchased tickets",
          testEndpoint: "POST /api/test/email-templates",
          payload: {
            type: "ticket",
            email: "<EMAIL>"
          }
        }
      ],
      configuration: {
        provider: "Resend",        from: process.env.EMAIL_FROM || "<EMAIL>",
        replyTo: "<EMAIL>",
        companyName: "VBTicket"
      }
    }
  });
}
