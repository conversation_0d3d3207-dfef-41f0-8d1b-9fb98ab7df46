/**
 * Simple test script to verify PDF generation functionality
 * Run with: node test-pdf-generation.js
 */

const fs = require('fs');
const path = require('path');

async function testPDFGeneration() {
  try {
    console.log('🧪 Testing PDF Generation...');
    
    // Import the PDF service
    const { generateTicketPDF } = require('./src/lib/services/pdf-ticket.service.ts');
    const { generateQRCodeData } = require('./src/lib/services/qr-code.service.ts');
    
    console.log('✅ Successfully imported PDF services');
    
    // Generate test QR data
    const qrData = generateQRCodeData({
      ticketId: "test-ticket-001",
      eventId: "test-event-001", 
      userId: "test-user-001",
      transactionId: "test-transaction-001",
      ticketTypeId: "test-ticket-type-001",
      eventDate: new Date(),
    });
    
    console.log('✅ Generated QR data');
    
    // Create test ticket data
    const ticketPDFData = {
      ticketId: "test-ticket-001",
      ticketNumber: "TKT-001-TEST",
      ticketType: "VIP",
      holderName: "Test Customer",
      qrData,
      event: {
        title: "Test Event PDF Generation",
        date: "Sabtu, 15 Juni 2025",
        time: "19:00 WIB",
        location: "Jakarta Convention Center",
        address: "Jl. Gatot Subroto, Jakarta Pusat, DKI Jakarta",
      },
      order: {
        invoiceNumber: "INV-TEST-001",
        totalAmount: 150000,
        paymentDate: "15 Juni 2025, 14:30 WIB",
      },
    };
    
    console.log('✅ Created test ticket data');
    
    // Generate PDF
    const pdfBuffer = await generateTicketPDF(ticketPDFData);
    
    console.log('✅ Generated PDF buffer');
    console.log(`📄 PDF size: ${pdfBuffer.length} bytes (${Math.round(pdfBuffer.length / 1024)} KB)`);
    
    // Save PDF to file for testing
    const outputPath = path.join(__dirname, 'test-ticket.pdf');
    fs.writeFileSync(outputPath, pdfBuffer);
    
    console.log(`✅ PDF saved to: ${outputPath}`);
    console.log('🎉 PDF generation test completed successfully!');
    
    return {
      success: true,
      pdfSize: pdfBuffer.length,
      outputPath,
    };
    
  } catch (error) {
    console.error('❌ PDF generation test failed:', error);
    return {
      success: false,
      error: error.message,
    };
  }
}

// Run the test
if (require.main === module) {
  testPDFGeneration()
    .then(result => {
      if (result.success) {
        console.log('\n✅ All tests passed!');
        process.exit(0);
      } else {
        console.log('\n❌ Tests failed!');
        process.exit(1);
      }
    })
    .catch(error => {
      console.error('\n💥 Unexpected error:', error);
      process.exit(1);
    });
}

module.exports = { testPDFGeneration };
