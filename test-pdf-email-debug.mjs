/**
 * Debug script to test PDF generation and email attachment
 * Run with: node test-pdf-email-debug.mjs
 */

import { jsPDF } from 'jspdf';
import fs from 'fs';

async function testPDFGeneration() {
  try {
    console.log('🧪 Testing PDF Generation with jsPDF...');
    
    // Create a simple PDF
    const doc = new jsPDF();
    
    // Add some content
    doc.setFontSize(20);
    doc.text('VBTicket', 105, 20, { align: 'center' });
    
    doc.setFontSize(16);
    doc.text('E-TICKET', 105, 35, { align: 'center' });
    
    doc.setFontSize(14);
    doc.text('Test Event PDF Generation', 105, 50, { align: 'center' });
    
    // Add ticket information
    doc.setFontSize(12);
    doc.text('Nomor Tiket: TKT-001-TEST', 20, 70);
    doc.text('<PERSON><PERSON>iket: VIP', 20, 80);
    doc.text('Pemegang Tiket: Test Customer', 20, 90);
    doc.text('Invoice: INV-TEST-001', 20, 100);
    
    // Add event details
    doc.text('DETAIL ACARA', 20, 120);
    doc.text('Tanggal & Waktu: Sabtu, 15 Juni 2025, 19:00 WIB', 20, 135);
    doc.text('Lokasi: Jakarta Convention Center', 20, 145);
    doc.text('Alamat: Jl. Gatot Subroto, Jakarta Pusat', 20, 155);
    
    // Add important message
    doc.text('Ini tiket Anda, silahkan ditukarkan saat penukaran', 105, 180, { align: 'center' });
    
    // Add instructions
    doc.text('PETUNJUK PENTING:', 20, 200);
    doc.setFontSize(10);
    doc.text('• Tunjukkan kode QR ini saat masuk ke venue', 20, 210);
    doc.text('• Bawa identitas yang sesuai dengan nama pemegang tiket', 20, 220);
    doc.text('• Datang 30 menit sebelum acara dimulai', 20, 230);
    doc.text('• Simpan tiket ini dengan baik', 20, 240);
    
    // Footer
    doc.setFontSize(10);
    doc.text('Terima kasih telah menggunakan VBTicket', 105, 280, { align: 'center' });
    
    // Convert to buffer
    const pdfArrayBuffer = doc.output('arraybuffer');
    const pdfBuffer = Buffer.from(pdfArrayBuffer);
    
    console.log('✅ PDF generated successfully');
    console.log(`📄 PDF size: ${pdfBuffer.length} bytes (${Math.round(pdfBuffer.length / 1024)} KB)`);
    console.log(`📄 Is Buffer: ${Buffer.isBuffer(pdfBuffer)}`);
    console.log(`📄 First 10 bytes: [${Array.from(pdfBuffer.slice(0, 10)).join(', ')}]`);
    
    // Save to file for verification
    fs.writeFileSync('test-debug-ticket.pdf', pdfBuffer);
    console.log('📁 PDF saved as: test-debug-ticket.pdf');
    
    // Test attachment format
    const attachment = {
      filename: 'test-ticket-attachment.pdf',
      content: pdfBuffer,
    };
    
    console.log('📎 Attachment object created:');
    console.log(`   - filename: ${attachment.filename}`);
    console.log(`   - content type: ${typeof attachment.content}`);
    console.log(`   - content size: ${attachment.content.length} bytes`);
    console.log(`   - is Buffer: ${Buffer.isBuffer(attachment.content)}`);
    
    // Test base64 encoding (alternative format)
    const base64Content = pdfBuffer.toString('base64');
    console.log(`📄 Base64 encoded size: ${base64Content.length} characters`);
    console.log(`📄 Base64 starts with: ${base64Content.substring(0, 50)}...`);
    
    return {
      success: true,
      pdfSize: pdfBuffer.length,
      attachment: {
        filename: attachment.filename,
        contentSize: attachment.content.length,
        isBuffer: Buffer.isBuffer(attachment.content),
      },
      base64Size: base64Content.length,
    };
    
  } catch (error) {
    console.error('❌ PDF generation test failed:', error);
    return {
      success: false,
      error: error.message,
    };
  }
}

async function testEmailAttachmentFormat() {
  try {
    console.log('\n🧪 Testing Email Attachment Formats...');
    
    // Generate a simple PDF
    const doc = new jsPDF();
    doc.text('Test PDF for Email Attachment', 20, 20);
    const pdfBuffer = Buffer.from(doc.output('arraybuffer'));
    
    // Test different attachment formats
    const formats = [
      {
        name: 'Buffer Content',
        attachment: {
          filename: 'test-buffer.pdf',
          content: pdfBuffer,
        }
      },
      {
        name: 'Base64 Content',
        attachment: {
          filename: 'test-base64.pdf',
          content: pdfBuffer.toString('base64'),
        }
      },
      {
        name: 'Buffer with ContentType',
        attachment: {
          filename: 'test-buffer-type.pdf',
          content: pdfBuffer,
          contentType: 'application/pdf',
        }
      },
      {
        name: 'Base64 with ContentType',
        attachment: {
          filename: 'test-base64-type.pdf',
          content: pdfBuffer.toString('base64'),
          contentType: 'application/pdf',
        }
      }
    ];
    
    formats.forEach((format, index) => {
      console.log(`\n📎 Format ${index + 1}: ${format.name}`);
      console.log(`   - filename: ${format.attachment.filename}`);
      console.log(`   - content type: ${typeof format.attachment.content}`);
      console.log(`   - content size: ${format.attachment.content.length}`);
      console.log(`   - has contentType: ${!!format.attachment.contentType}`);
      console.log(`   - is Buffer: ${Buffer.isBuffer(format.attachment.content)}`);
    });
    
    return {
      success: true,
      testedFormats: formats.length,
    };
    
  } catch (error) {
    console.error('❌ Email attachment format test failed:', error);
    return {
      success: false,
      error: error.message,
    };
  }
}

// Run the tests
async function runAllTests() {
  console.log('🚀 Starting PDF Email Debug Tests...\n');
  
  const pdfTest = await testPDFGeneration();
  const attachmentTest = await testEmailAttachmentFormat();
  
  console.log('\n📊 Test Results Summary:');
  console.log(`   PDF Generation: ${pdfTest.success ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`   Attachment Format: ${attachmentTest.success ? '✅ PASS' : '❌ FAIL'}`);
  
  if (pdfTest.success && attachmentTest.success) {
    console.log('\n🎉 All tests passed! PDF generation and attachment formatting work correctly.');
    console.log('\n💡 Next steps:');
    console.log('   1. Check email service configuration (RESEND_API_KEY)');
    console.log('   2. Verify Resend API attachment format requirements');
    console.log('   3. Test actual email sending with debug endpoint');
  } else {
    console.log('\n❌ Some tests failed. Check the errors above.');
  }
  
  return {
    pdfGeneration: pdfTest.success,
    attachmentFormat: attachmentTest.success,
    overall: pdfTest.success && attachmentTest.success,
  };
}

runAllTests()
  .then(results => {
    process.exit(results.overall ? 0 : 1);
  })
  .catch(error => {
    console.error('\n💥 Unexpected error:', error);
    process.exit(1);
  });
