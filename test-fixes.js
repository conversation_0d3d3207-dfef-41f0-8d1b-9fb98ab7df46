#!/usr/bin/env node

/**
 * Test script to verify real-time updates and email delivery fixes
 * Run with: node test-fixes.js
 */

const orderId = "cmbxgyo6m0004uoz0ocvtehpm"; // Replace with your actual order ID
const baseUrl = "http://localhost:3000"; // Change for production

async function testFixes() {
  console.log("🧪 Testing VBTicket Real-time Updates & Email Delivery Fixes\n");

  // Test 1: Debug endpoint
  console.log("1️⃣ Testing debug endpoint...");
  try {
    const response = await fetch(`${baseUrl}/api/debug/realtime-email`, {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({ orderId, action: "test-all" }),
    });

    const result = await response.json();
    
    if (result.success) {
      console.log("✅ Debug endpoint working");
      console.log(`   Tests passed: ${result.debug.summary.successfulTests}/${result.debug.summary.totalTests}`);
      
      if (result.debug.summary.recommendations.length > 0) {
        console.log("⚠️  Recommendations:");
        result.debug.summary.recommendations.forEach(rec => {
          console.log(`   - ${rec}`);
        });
      }
    } else {
      console.log("❌ Debug endpoint failed:", result.error);
    }
  } catch (error) {
    console.log("❌ Debug endpoint error:", error.message);
  }

  console.log();

  // Test 2: Status endpoint (real-time updates)
  console.log("2️⃣ Testing status endpoint (real-time updates)...");
  try {
    const response = await fetch(`${baseUrl}/api/public/orders/${orderId}/status`);
    
    if (response.ok) {
      const result = await response.json();
      console.log("✅ Status endpoint working");
      console.log(`   Order status: ${result.data.status}`);
      console.log(`   Has QR codes: ${result.data.hasQRCodes}`);
    } else {
      console.log(`❌ Status endpoint failed: ${response.status} ${response.statusText}`);
      
      if (response.status === 401) {
        console.log("   💡 Try with sessionId parameter for guest access");
      }
    }
  } catch (error) {
    console.log("❌ Status endpoint error:", error.message);
  }

  console.log();

  // Test 3: Email configuration
  console.log("3️⃣ Testing email configuration...");
  try {
    const response = await fetch(`${baseUrl}/api/debug/realtime-email`, {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({ orderId, action: "test-email" }),
    });

    const result = await response.json();
    
    if (result.success && result.debug.tests.emailConfig?.success) {
      console.log("✅ Email configuration working");
      console.log(`   Email to: ${result.debug.tests.emailConfig.data.emailTo}`);
      console.log(`   Has QR codes: ${result.debug.tests.emailConfig.data.hasQRCodes}`);
    } else {
      console.log("❌ Email configuration failed");
      if (result.debug.tests.emailConfig?.error) {
        console.log(`   Error: ${result.debug.tests.emailConfig.error}`);
      }
    }
  } catch (error) {
    console.log("❌ Email configuration error:", error.message);
  }

  console.log();

  // Test 4: QR code generation
  console.log("4️⃣ Testing QR code generation...");
  try {
    const response = await fetch(`${baseUrl}/api/debug/realtime-email`, {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({ orderId, action: "generate-qr" }),
    });

    const result = await response.json();
    
    if (result.success && result.debug.tests.qrGeneration?.success) {
      console.log("✅ QR code generation working");
      console.log(`   Generated: ${result.debug.tests.qrGeneration.data.generatedCount} QR codes`);
    } else {
      console.log("❌ QR code generation failed");
      if (result.debug.tests.qrGeneration?.error) {
        console.log(`   Error: ${result.debug.tests.qrGeneration.error}`);
      }
    }
  } catch (error) {
    console.log("❌ QR code generation error:", error.message);
  }

  console.log();

  // Summary
  console.log("📋 Summary:");
  console.log("   If all tests pass, your fixes are working correctly!");
  console.log("   If any tests fail, check the troubleshooting guide.");
  console.log();
  console.log("📖 Next steps:");
  console.log("   1. Set up environment variables if missing");
  console.log("   2. Test with a real order in your browser");
  console.log("   3. Verify real-time updates work on pending payment page");
  console.log("   4. Check email delivery after payment verification");
}

// Run the tests
testFixes().catch(console.error);
