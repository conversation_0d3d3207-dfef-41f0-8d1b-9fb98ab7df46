# Since the ".env" file is gitignored, you can use the ".env.example" file to
# build a new ".env" file when you clone the repo. Keep this file up-to-date
# when you add new variables to `.env`.

# This file will be committed to version control, so make sure not to have any
# secrets in it. If you are cloning this repo, create a copy of this file named
# ".env" and populate it with your secrets.

# When adding additional environment variables, the schema in "/src/env.js"
# should be updated accordingly.

# Next Auth
# You can generate a new secret on the command line with:
# npx auth secret
# https://next-auth.js.org/configuration/options#secret
NEXTAUTH_SECRET=""
NEXTAUTH_URL="http://localhost:3000"

# Next Auth Google Provider
GOOGLE_CLIENT_ID=""
GOOGLE_CLIENT_SECRET=""

# Prisma with Supabase
# Get these from your Supabase project settings → Database
# DATABASE_URL should use connection pooling (port 6543)
# DIRECT_URL should use direct connection (port 5432)
DATABASE_URL="postgresql://postgres.PROJECT_REF:<EMAIL>:6543/postgres"
DIRECT_URL="**************************************************************/postgres"

# For local development with Docker
# DATABASE_URL="postgresql://postgres:password@localhost:5432/vbtix"
# DIRECT_URL="postgresql://postgres:password@localhost:5432/vbtix"

# Google OAuth
GOOGLE_CLIENT_ID=""
GOOGLE_CLIENT_SECRET=""

# Email (Resend.com)
RESEND_API_KEY=""
EMAIL_FROM="<EMAIL>"

# Midtrans Payment Gateway
MIDTRANS_SERVER_KEY=""
MIDTRANS_CLIENT_KEY=""

# Xendit Payment Gateway
XENDIT_SECRET_KEY=""
XENDIT_WEBHOOK_TOKEN=""

# Payment Gateway Control
# Set to "true" to enable Xendit, leave empty or "false" for test mode
NEXT_PUBLIC_XENDIT_ENABLED=""

# Cloudinary
NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME=""
NEXT_PUBLIC_CLOUDINARY_API_KEY=""
CLOUDINARY_API_SECRET=""
NEXT_PUBLIC_CLOUDINARY_UPLOAD_PRESET=""
