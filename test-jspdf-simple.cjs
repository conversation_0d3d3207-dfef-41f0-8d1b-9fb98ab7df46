/**
 * Simple test to verify jsPDF works
 */

const fs = require('fs');

async function testJsPDF() {
  try {
    console.log('🧪 Testing jsPDF...');
    
    // Import jsPDF
    const { jsPDF } = require('jspdf');
    
    console.log('✅ jsPDF imported successfully');
    
    // Create a simple PDF
    const doc = new jsPDF();
    doc.text('Hello VBTicket!', 20, 20);
    doc.text('This is a test PDF generated with jsPDF', 20, 30);
    
    // Save to file
    const pdfBuffer = Buffer.from(doc.output('arraybuffer'));
    fs.writeFileSync('test-simple.pdf', pdfBuffer);
    
    console.log('✅ Simple PDF generated successfully');
    console.log(`📄 PDF size: ${pdfBuffer.length} bytes`);
    console.log('📁 Saved as: test-simple.pdf');
    
    return { success: true, size: pdfBuffer.length };
    
  } catch (error) {
    console.error('❌ jsPDF test failed:', error);
    return { success: false, error: error.message };
  }
}

// Run the test
if (require.main === module) {
  testJsPDF()
    .then(result => {
      if (result.success) {
        console.log('\n✅ jsPDF test passed!');
        process.exit(0);
      } else {
        console.log('\n❌ jsPDF test failed!');
        process.exit(1);
      }
    })
    .catch(error => {
      console.error('\n💥 Unexpected error:', error);
      process.exit(1);
    });
}

module.exports = { testJsPDF };
